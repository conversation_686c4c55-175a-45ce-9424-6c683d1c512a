# تقرير مشروع: اختيار الميزات باستخدام خوارزمية وراثية (GA) — BIA601 (RAFD)

## 1. الملخّص التنفيذي
الهدف من المشروع هو تنفيذ خوارزمية وراثية لاختيار الميزات (GA) ومقارنتها مع طرق تقليدية وإحصائية (Lasso, PCA, Chi-square, ANOVA)، وتقديم منتج برمجي يسمح برفع ملفات CSV وتشغيل التحليل بسهولة. يتضمن المشروع واجهة Streamlit لتشغيل التحليل وإخراج النتائج.

## 2. المتطلبات المرجعية
- دعم البيانات الرقمية (يفضل نصية). تم دعم CSV كصيغة تحميل.
- لا حاجة لاستخدام نماذج تعلم عميق.
- استخدام تابع لياقة لقياس جودة الميزات.
- تقليم الميزات وتقليل الأبعاد.
- مقارنة مع Lasso, PCA, واختبارات إحصائية مثل Chi-square.
- توثيق وشرح واضح.

## 3. البيانات المستخدمة
المشروع يتضمن أمثلة جاهزة (Breast Cancer من sklearn). يمكن للمستخدم رفع ملف CSV يحتوي على عمود الهدف target.

## 4. منهجية الحل
### 4.1 خوارزمية وراثية (GA)
- تمثيل الكروموسوم: قناع ثنائي (0/1) لكل ميزة.
- تابع اللياقة: متوسط دقة RandomForest عبر cross-validation.
- معاملات افتراضية: population_size=30, generations=25, crossover=0.8, mutation=0.02.
- آليات اختيار: Tournament selection، Single-point crossover، Bit-flip mutation.

### 4.2 المقارنات
- LassoCV لاختيار الميزات ذات الأوزان غير الصفرية.
- SelectKBest(chi2) لاختبار كاي تربيع.
- SelectKBest(f_classif) لاختبار ANOVA.
- PCA لتقليل الأبعاد إلى أول k مركبة.

## 5. النتائج
عند تشغيل `web/app.py` أو `src/compare_methods.py` سيُنتَج جدول يقارن كل طريقة في: الدقة (accuracy)، F1 (f1_macro)، وعدد الميزات.

## 6. كيفية التشغيل
1. ثبت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
2. لتشغيل الويب:
   ```
   streamlit run web/app.py
   ```
3. لتشغيل المقارنات سطريًا:
   ```
   python -m src.compare_methods
   ```

## 7. ملاحظات تسليم
- تم تضمين توثيق عربي وملف README.
- المشروع منظّم في مجلدات src, web, docs, results, examples.
- يمكن تحسين GA بإضافة multi-objective fitness (accuracy vs #features) وتعديل المعاملات.

## 8. اقتراحات مستقبلية
- دعم بيانات نصية (feature engineering للنص).
- إضافة تقارير PDF مُولّدة تلقائياً.
- استكمال اختبارات وحدات للتأكد من سلامة الوحدات.
