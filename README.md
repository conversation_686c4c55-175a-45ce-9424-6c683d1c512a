مشروع: اختيار الميزات باستخدام خوارزمية وراثية (GA) — BIA601 (RAFD)
--------------------------------------------------------------
المحتويات:
- src/: كود المشروع (GA + مقارنة الأساليب)
- web/: تطبيق Streamlit لرفع CSV وتشغيل التحليل
- docs/: تقرير عربي جاهز للطباعة
- results/: مجلد لحفظ نواتج التشغيل
- examples/: مكان لوضع عينات البيانات

خطوات سريعة للتشغيل:
1. تثبيت المتطلبات: pip install -r requirements.txt
2. تشغيل الويب: streamlit run web/app.py
3. أو تشغيل المقارنات مباشرة: python -m src.compare_methods

ملاحظة: تأكد من أن عمود الهدف في ملفات CSV المرفوعة يسمى 'target' أو تختاره من الواجهة.
